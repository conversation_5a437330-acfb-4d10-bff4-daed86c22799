"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  ComposedChart,
  Area,
  AreaChart,
} from "recharts";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ProcessedKlineData } from "@/types/trading";

interface KlineChartProps {
  data: {
    monthly: ProcessedKlineData[];
    daily: ProcessedKlineData[];
    hourly: ProcessedKlineData[];
    thirtyMin: ProcessedKlineData[];
    oneMin: ProcessedKlineData[];
  };
  symbol: string;
  className?: string;
  lastUpdateTime?: Date | null;
  isPolling?: boolean;
  countdown?: number;
}

export function KlineChart({
  data,
  symbol,
  className,
  lastUpdateTime,
  isPolling = false,
  countdown = 0,
}: KlineChartProps) {
  const [activeTimeframe, setActiveTimeframe] =
    useState<keyof typeof data>("daily");

  const timeframes = [
    { key: "oneMin" as const, label: "1分钟", period: "1小时" },
    { key: "thirtyMin" as const, label: "30分钟", period: "24小时" },
    { key: "hourly" as const, label: "1小时", period: "7天" },
    { key: "daily" as const, label: "1天", period: "30天" },
    { key: "monthly" as const, label: "1月", period: "36个月" },
  ];

  const formatTimestamp = (timestamp: number, timeframe: string) => {
    const date = new Date(timestamp);

    switch (timeframe) {
      case "oneMin":
      case "thirtyMin":
        return date.toLocaleTimeString("zh-CN", {
          hour: "2-digit",
          minute: "2-digit",
        });
      case "hourly":
        return date.toLocaleDateString("zh-CN", {
          month: "short",
          day: "numeric",
          hour: "2-digit",
        });
      case "daily":
        return date.toLocaleDateString("zh-CN", {
          month: "short",
          day: "numeric",
        });
      case "monthly":
        return date.toLocaleDateString("zh-CN", {
          year: "numeric",
          month: "short",
        });
      default:
        return date.toLocaleDateString("zh-CN");
    }
  };

  const formatPrice = (price: number) => {
    if (price >= 1) {
      return price.toFixed(2);
    } else if (price >= 0.01) {
      return price.toFixed(4);
    } else {
      return price.toFixed(6);
    }
  };

  const formatVolume = (volume: number) => {
    if (volume >= 1000000) {
      return `${(volume / 1000000).toFixed(1)}M`;
    } else if (volume >= 1000) {
      return `${(volume / 1000).toFixed(1)}K`;
    } else {
      return volume.toFixed(0);
    }
  };

  const currentData = data[activeTimeframe] || [];
  const latestData = currentData[currentData.length - 1];

  // 准备图表数据
  const chartData = currentData.map((item) => ({
    ...item,
    time: formatTimestamp(item.timestamp, activeTimeframe),
    priceChange: item.changePercent,
  }));

  // 计算统计信息
  const stats = {
    high: Math.max(...currentData.map((d) => d.high)),
    low: Math.min(...currentData.map((d) => d.low)),
    avgVolume:
      currentData.reduce((sum, d) => sum + d.volume, 0) / currentData.length,
    totalChange: latestData
      ? ((latestData.close - currentData[0].open) / currentData[0].open) * 100
      : 0,
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium">{label}</p>
          <div className="space-y-1 text-sm">
            <p>
              开盘:{" "}
              <span className="font-medium">${formatPrice(data.open)}</span>
            </p>
            <p>
              最高:{" "}
              <span className="font-medium text-green-600">
                ${formatPrice(data.high)}
              </span>
            </p>
            <p>
              最低:{" "}
              <span className="font-medium text-red-600">
                ${formatPrice(data.low)}
              </span>
            </p>
            <p>
              收盘:{" "}
              <span className="font-medium">${formatPrice(data.close)}</span>
            </p>
            <p>
              成交量:{" "}
              <span className="font-medium">{formatVolume(data.volume)}</span>
            </p>
            <p>
              涨跌:{" "}
              <span
                className={`font-medium ${
                  data.changePercent >= 0 ? "text-green-600" : "text-red-600"
                }`}
              >
                {data.changePercent >= 0 ? "+" : ""}
                {data.changePercent.toFixed(2)}%
              </span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <span>{symbol} 价格走势</span>
            {latestData && (
              <Badge
                variant={
                  latestData.changePercent >= 0 ? "default" : "destructive"
                }
              >
                {latestData.changePercent >= 0 ? "+" : ""}
                {latestData.changePercent.toFixed(2)}%
              </Badge>
            )}
          </CardTitle>

          <div className="flex space-x-1">
            {timeframes.map((tf) => (
              <Button
                key={tf.key}
                variant={activeTimeframe === tf.key ? "default" : "outline"}
                size="sm"
                onClick={() => setActiveTimeframe(tf.key)}
              >
                {tf.label}
              </Button>
            ))}
          </div>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <p className="text-gray-500">最高价</p>
            <p className="font-semibold text-green-600">
              ${formatPrice(stats.high)}
            </p>
          </div>
          <div>
            <p className="text-gray-500">最低价</p>
            <p className="font-semibold text-red-600">
              ${formatPrice(stats.low)}
            </p>
          </div>
          <div>
            <p className="text-gray-500">平均成交量</p>
            <p className="font-semibold">{formatVolume(stats.avgVolume)}</p>
          </div>
          <div>
            <p className="text-gray-500">总涨跌</p>
            <p
              className={`font-semibold ${
                stats.totalChange >= 0 ? "text-green-600" : "text-red-600"
              }`}
            >
              {stats.totalChange >= 0 ? "+" : ""}
              {stats.totalChange.toFixed(2)}%
            </p>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="price" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="price">价格走势</TabsTrigger>
            <TabsTrigger value="volume">成交量</TabsTrigger>
            <TabsTrigger value="change">涨跌幅</TabsTrigger>
          </TabsList>

          <TabsContent value="price" className="mt-4">
            <ResponsiveContainer width="100%" height={400}>
              <AreaChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="time"
                  tick={{ fontSize: 12 }}
                  interval="preserveStartEnd"
                />
                <YAxis
                  domain={["dataMin - 1", "dataMax + 1"]}
                  tick={{ fontSize: 12 }}
                  tickFormatter={formatPrice}
                />
                <Tooltip content={<CustomTooltip />} />
                <Area
                  type="monotone"
                  dataKey="close"
                  stroke="#2563eb"
                  fill="#3b82f6"
                  fillOpacity={0.1}
                  strokeWidth={2}
                />
                <Line
                  type="monotone"
                  dataKey="high"
                  stroke="#10b981"
                  strokeWidth={1}
                  dot={false}
                />
                <Line
                  type="monotone"
                  dataKey="low"
                  stroke="#ef4444"
                  strokeWidth={1}
                  dot={false}
                />
              </AreaChart>
            </ResponsiveContainer>
          </TabsContent>

          <TabsContent value="volume" className="mt-4">
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="time"
                  tick={{ fontSize: 12 }}
                  interval="preserveStartEnd"
                />
                <YAxis tick={{ fontSize: 12 }} tickFormatter={formatVolume} />
                <Tooltip
                  formatter={(value: number) => [formatVolume(value), "成交量"]}
                  labelFormatter={(label) => `时间: ${label}`}
                />
                <Bar dataKey="volume" fill="#8884d8" opacity={0.8} />
              </BarChart>
            </ResponsiveContainer>
          </TabsContent>

          <TabsContent value="change" className="mt-4">
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="time"
                  tick={{ fontSize: 12 }}
                  interval="preserveStartEnd"
                />
                <YAxis
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) => `${value.toFixed(1)}%`}
                />
                <Tooltip
                  formatter={(value: number) => [
                    `${value.toFixed(2)}%`,
                    "涨跌幅",
                  ]}
                  labelFormatter={(label) => `时间: ${label}`}
                />
                <Bar dataKey="priceChange" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </TabsContent>
        </Tabs>

        <div className="mt-4 text-xs text-gray-500 text-center space-y-1">
          <div>
            数据周期:{" "}
            {timeframes.find((tf) => tf.key === activeTimeframe)?.period} |
            最后更新:{" "}
            {lastUpdateTime
              ? lastUpdateTime.toLocaleTimeString("zh-CN")
              : "未知"}
          </div>
          {isPolling && (
            <div className="flex items-center justify-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-green-600">
                实时轮询中 {countdown > 0 && `(${countdown}s后更新)`}
              </span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
