"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Brain,
  TrendingUp,
  TrendingDown,
  Target,
  Users,
  Calculator,
  BarChart3,
  AlertTriangle,
  CheckCircle,
  Info,
  Zap,
  Eye,
} from "lucide-react";
import { GameTheoryAnalysis, ProbabilityAnalysis } from "@/types/trading";

interface GameTheoryProbabilityAnalysisProps {
  gameTheory: GameTheoryAnalysis;
  probability: ProbabilityAnalysis;
  symbol: string;
  className?: string;
}

export function GameTheoryProbabilityAnalysis({
  gameTheory,
  probability,
  symbol,
  className,
}: GameTheoryProbabilityAnalysisProps) {
  const [activeTab, setActiveTab] = useState("gameTheory");

  const getStrategyColor = (strategy: string) => {
    switch (strategy) {
      case "ACCUMULATE":
      case "BUY":
        return "bg-green-500";
      case "DISTRIBUTE":
      case "SELL":
        return "bg-red-500";
      case "MANIPULATE":
        return "bg-orange-500";
      case "MAINTAIN":
      case "HOLD":
        return "bg-gray-500";
      default:
        return "bg-blue-500";
    }
  };

  const getBehaviorColor = (behavior: string) => {
    switch (behavior) {
      case "PANIC":
        return "bg-red-500";
      case "FOMO":
        return "bg-orange-500";
      case "RATIONAL":
        return "bg-green-500";
      case "ACCUMULATING":
        return "bg-blue-500";
      default:
        return "bg-gray-500";
    }
  };

  const getProbabilityColor = (value: number) => {
    if (value >= 70) return "text-green-600";
    if (value >= 50) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <div className={`space-y-6 ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Brain className="h-5 w-5" />
            <span>博弈论 & 概率论分析 - {symbol}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger
                value="gameTheory"
                className="flex items-center space-x-2"
              >
                <Users className="h-4 w-4" />
                <span>博弈论分析</span>
              </TabsTrigger>
              <TabsTrigger
                value="probability"
                className="flex items-center space-x-2"
              >
                <Calculator className="h-4 w-4" />
                <span>概率论分析</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="gameTheory" className="space-y-6">
              {/* 市场参与者分析 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Users className="h-4 w-4" />
                    <span>市场参与者博弈分析</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* 庄家分析 */}
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-semibold mb-3 flex items-center space-x-2">
                      <Target className="h-4 w-4" />
                      <span>庄家 (Market Makers)</span>
                    </h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <span className="text-sm text-gray-500">策略</span>
                        <Badge
                          className={`ml-2 ${getStrategyColor(
                            gameTheory.playerAnalysis.marketMakers.strategy
                          )}`}
                        >
                          {gameTheory.playerAnalysis.marketMakers.strategy}
                        </Badge>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">预期走向</span>
                        <Badge
                          className={`ml-2 ${getStrategyColor(
                            gameTheory.playerAnalysis.marketMakers.expectedMove
                          )}`}
                        >
                          {gameTheory.playerAnalysis.marketMakers.expectedMove}
                        </Badge>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">信心度</span>
                        <Progress
                          value={
                            gameTheory.playerAnalysis.marketMakers.confidence
                          }
                          className="w-20 ml-2"
                        />
                        <span className="text-sm ml-2">
                          {gameTheory.playerAnalysis.marketMakers.confidence}%
                        </span>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">时间视野</span>
                        <Badge variant="outline" className="ml-2">
                          {gameTheory.playerAnalysis.marketMakers.timeHorizon}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  {/* 机构分析 */}
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-semibold mb-3 flex items-center space-x-2">
                      <BarChart3 className="h-4 w-4" />
                      <span>机构 (Institutions)</span>
                    </h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <span className="text-sm text-gray-500">策略</span>
                        <Badge
                          className={`ml-2 ${getStrategyColor(
                            gameTheory.playerAnalysis.institutions.strategy
                          )}`}
                        >
                          {gameTheory.playerAnalysis.institutions.strategy}
                        </Badge>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">仓位</span>
                        <Badge
                          className={`ml-2 ${getStrategyColor(
                            gameTheory.playerAnalysis.institutions.positioning
                          )}`}
                        >
                          {gameTheory.playerAnalysis.institutions.positioning}
                        </Badge>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">信心度</span>
                        <Progress
                          value={
                            gameTheory.playerAnalysis.institutions.confidence
                          }
                          className="w-20 ml-2"
                        />
                        <span className="text-sm ml-2">
                          {gameTheory.playerAnalysis.institutions.confidence}%
                        </span>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">影响力</span>
                        <Progress
                          value={
                            gameTheory.playerAnalysis.institutions.influence
                          }
                          className="w-20 ml-2"
                        />
                        <span className="text-sm ml-2">
                          {gameTheory.playerAnalysis.institutions.influence}%
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* 散户分析 */}
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-semibold mb-3 flex items-center space-x-2">
                      <Users className="h-4 w-4" />
                      <span>散户 (Retail Traders)</span>
                    </h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <span className="text-sm text-gray-500">行为模式</span>
                        <Badge
                          className={`ml-2 ${getBehaviorColor(
                            gameTheory.playerAnalysis.retailers.behavior
                          )}`}
                        >
                          {gameTheory.playerAnalysis.retailers.behavior}
                        </Badge>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">杠杆水平</span>
                        <Badge variant="outline" className="ml-2">
                          {gameTheory.playerAnalysis.retailers.leverage}
                        </Badge>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">情绪指数</span>
                        <Progress
                          value={gameTheory.playerAnalysis.retailers.sentiment}
                          className="w-20 ml-2"
                        />
                        <span className="text-sm ml-2">
                          {gameTheory.playerAnalysis.retailers.sentiment}%
                        </span>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">爆仓风险</span>
                        <Progress
                          value={
                            gameTheory.playerAnalysis.retailers.liquidationRisk
                          }
                          className="w-20 ml-2"
                        />
                        <span className="text-sm ml-2">
                          {gameTheory.playerAnalysis.retailers.liquidationRisk}%
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 均衡分析 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Target className="h-4 w-4" />
                    <span>博弈均衡分析</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-semibold mb-2">纳什均衡</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">
                            均衡价格
                          </span>
                          <span className="font-medium">
                            $
                            {gameTheory.equilibriumAnalysis.nashEquilibrium.price.toFixed(
                              4
                            )}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">稳定性</span>
                          <div className="flex items-center space-x-2">
                            <Progress
                              value={
                                gameTheory.equilibriumAnalysis.nashEquilibrium
                                  .stability
                              }
                              className="w-16"
                            />
                            <span className="text-sm">
                              {
                                gameTheory.equilibriumAnalysis.nashEquilibrium
                                  .stability
                              }
                              %
                            </span>
                          </div>
                        </div>
                        <p className="text-sm text-gray-600 mt-2">
                          {
                            gameTheory.equilibriumAnalysis.nashEquilibrium
                              .description
                          }
                        </p>
                      </div>
                    </div>

                    <div className="p-4 border rounded-lg">
                      <h4 className="font-semibold mb-2">信息不对称</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">
                            不对称程度
                          </span>
                          <div className="flex items-center space-x-2">
                            <Progress
                              value={
                                gameTheory.equilibriumAnalysis
                                  .informationAsymmetry.level
                              }
                              className="w-16"
                            />
                            <span className="text-sm">
                              {
                                gameTheory.equilibriumAnalysis
                                  .informationAsymmetry.level
                              }
                              %
                            </span>
                          </div>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">
                            信息优势方
                          </span>
                          <Badge variant="outline">
                            {
                              gameTheory.equilibriumAnalysis
                                .informationAsymmetry.advantage
                            }
                          </Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">
                            影响程度
                          </span>
                          <Badge variant="outline">
                            {
                              gameTheory.equilibriumAnalysis
                                .informationAsymmetry.impact
                            }
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <h4 className="font-semibold mb-2">占优策略</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">主导玩家</span>
                        <Badge className="bg-blue-500">
                          {
                            gameTheory.equilibriumAnalysis.dominantStrategy
                              .player
                          }
                        </Badge>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">策略描述</span>
                        <p className="text-sm mt-1">
                          {
                            gameTheory.equilibriumAnalysis.dominantStrategy
                              .strategy
                          }
                        </p>
                      </div>
                      <div>
                        <span className="text-sm text-gray-500">推理过程</span>
                        <p className="text-sm mt-1 text-gray-600">
                          {
                            gameTheory.equilibriumAnalysis.dominantStrategy
                              .reasoning
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 策略建议 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4" />
                    <span>博弈论策略建议</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <span className="text-sm text-gray-500">最优策略</span>
                      <Badge
                        className={`${getStrategyColor(
                          gameTheory.strategicRecommendation.optimalStrategy
                        )} text-white`}
                      >
                        {gameTheory.strategicRecommendation.optimalStrategy}
                      </Badge>
                    </div>
                    <div>
                      <span className="text-sm text-gray-500">策略推理</span>
                      <p className="text-sm mt-1">
                        {gameTheory.strategicRecommendation.reasoning}
                      </p>
                    </div>
                    <div>
                      <span className="text-sm text-gray-500">应对策略</span>
                      <p className="text-sm mt-1">
                        {gameTheory.strategicRecommendation.counterStrategy}
                      </p>
                    </div>
                    <div>
                      <span className="text-sm text-gray-500">风险缓解</span>
                      <p className="text-sm mt-1">
                        {gameTheory.strategicRecommendation.riskMitigation}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="probability" className="space-y-6">
              {/* 贝叶斯推理 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Calculator className="h-4 w-4" />
                    <span>贝叶斯推理分析</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-semibold mb-3">先验概率</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm">看涨</span>
                          <div className="flex items-center space-x-2">
                            <Progress
                              value={
                                probability.bayesianInference.priorProbability
                                  .bullish
                              }
                              className="w-16"
                            />
                            <span className="text-sm">
                              {
                                probability.bayesianInference.priorProbability
                                  .bullish
                              }
                              %
                            </span>
                          </div>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">看跌</span>
                          <div className="flex items-center space-x-2">
                            <Progress
                              value={
                                probability.bayesianInference.priorProbability
                                  .bearish
                              }
                              className="w-16"
                            />
                            <span className="text-sm">
                              {
                                probability.bayesianInference.priorProbability
                                  .bearish
                              }
                              %
                            </span>
                          </div>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">中性</span>
                          <div className="flex items-center space-x-2">
                            <Progress
                              value={
                                probability.bayesianInference.priorProbability
                                  .neutral
                              }
                              className="w-16"
                            />
                            <span className="text-sm">
                              {
                                probability.bayesianInference.priorProbability
                                  .neutral
                              }
                              %
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="p-4 border rounded-lg">
                      <h4 className="font-semibold mb-3">似然函数</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm">技术信号</span>
                          <div className="flex items-center space-x-2">
                            <Progress
                              value={
                                probability.bayesianInference.likelihood
                                  .technicalSignals
                              }
                              className="w-16"
                            />
                            <span className="text-sm">
                              {
                                probability.bayesianInference.likelihood
                                  .technicalSignals
                              }
                              %
                            </span>
                          </div>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">成交量确认</span>
                          <div className="flex items-center space-x-2">
                            <Progress
                              value={
                                probability.bayesianInference.likelihood
                                  .volumeConfirmation
                              }
                              className="w-16"
                            />
                            <span className="text-sm">
                              {
                                probability.bayesianInference.likelihood
                                  .volumeConfirmation
                              }
                              %
                            </span>
                          </div>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">市场结构</span>
                          <div className="flex items-center space-x-2">
                            <Progress
                              value={
                                probability.bayesianInference.likelihood
                                  .marketStructure
                              }
                              className="w-16"
                            />
                            <span className="text-sm">
                              {
                                probability.bayesianInference.likelihood
                                  .marketStructure
                              }
                              %
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="p-4 border rounded-lg">
                      <h4 className="font-semibold mb-3">后验概率</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm">看涨</span>
                          <div className="flex items-center space-x-2">
                            <Progress
                              value={
                                probability.bayesianInference
                                  .posteriorProbability.bullish
                              }
                              className="w-16"
                            />
                            <span
                              className={`text-sm ${getProbabilityColor(
                                probability.bayesianInference
                                  .posteriorProbability.bullish
                              )}`}
                            >
                              {
                                probability.bayesianInference
                                  .posteriorProbability.bullish
                              }
                              %
                            </span>
                          </div>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">看跌</span>
                          <div className="flex items-center space-x-2">
                            <Progress
                              value={
                                probability.bayesianInference
                                  .posteriorProbability.bearish
                              }
                              className="w-16"
                            />
                            <span
                              className={`text-sm ${getProbabilityColor(
                                probability.bayesianInference
                                  .posteriorProbability.bearish
                              )}`}
                            >
                              {
                                probability.bayesianInference
                                  .posteriorProbability.bearish
                              }
                              %
                            </span>
                          </div>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">中性</span>
                          <div className="flex items-center space-x-2">
                            <Progress
                              value={
                                probability.bayesianInference
                                  .posteriorProbability.neutral
                              }
                              className="w-16"
                            />
                            <span className="text-sm">
                              {
                                probability.bayesianInference
                                  .posteriorProbability.neutral
                              }
                              %
                            </span>
                          </div>
                        </div>
                        <div className="mt-3 pt-2 border-t">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">
                              总体信心度
                            </span>
                            <span
                              className={`text-sm font-bold ${getProbabilityColor(
                                probability.bayesianInference
                                  .posteriorProbability.confidence
                              )}`}
                            >
                              {
                                probability.bayesianInference
                                  .posteriorProbability.confidence
                              }
                              %
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 条件概率分析 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <BarChart3 className="h-4 w-4" />
                    <span>条件概率分析</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-semibold mb-3 flex items-center space-x-2">
                        <TrendingUp className="h-4 w-4 text-green-500" />
                        <span>上涨概率</span>
                      </h4>
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm">技术指标支持</span>
                          <span
                            className={`text-sm font-medium ${getProbabilityColor(
                              probability.conditionalProbabilities.upMove
                                .givenTechnicals
                            )}`}
                          >
                            {
                              probability.conditionalProbabilities.upMove
                                .givenTechnicals
                            }
                            %
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">成交量支持</span>
                          <span
                            className={`text-sm font-medium ${getProbabilityColor(
                              probability.conditionalProbabilities.upMove
                                .givenVolume
                            )}`}
                          >
                            {
                              probability.conditionalProbabilities.upMove
                                .givenVolume
                            }
                            %
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">庄家活动支持</span>
                          <span
                            className={`text-sm font-medium ${getProbabilityColor(
                              probability.conditionalProbabilities.upMove
                                .givenMakerActivity
                            )}`}
                          >
                            {
                              probability.conditionalProbabilities.upMove
                                .givenMakerActivity
                            }
                            %
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="p-4 border rounded-lg">
                      <h4 className="font-semibold mb-3 flex items-center space-x-2">
                        <TrendingDown className="h-4 w-4 text-red-500" />
                        <span>下跌概率</span>
                      </h4>
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm">技术指标支持</span>
                          <span
                            className={`text-sm font-medium ${getProbabilityColor(
                              probability.conditionalProbabilities.downMove
                                .givenTechnicals
                            )}`}
                          >
                            {
                              probability.conditionalProbabilities.downMove
                                .givenTechnicals
                            }
                            %
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">成交量支持</span>
                          <span
                            className={`text-sm font-medium ${getProbabilityColor(
                              probability.conditionalProbabilities.downMove
                                .givenVolume
                            )}`}
                          >
                            {
                              probability.conditionalProbabilities.downMove
                                .givenVolume
                            }
                            %
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">庄家活动支持</span>
                          <span
                            className={`text-sm font-medium ${getProbabilityColor(
                              probability.conditionalProbabilities.downMove
                                .givenMakerActivity
                            )}`}
                          >
                            {
                              probability.conditionalProbabilities.downMove
                                .givenMakerActivity
                            }
                            %
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="p-4 border rounded-lg">
                      <h4 className="font-semibold mb-3 flex items-center space-x-2">
                        <Zap className="h-4 w-4 text-blue-500" />
                        <span>突破成功概率</span>
                      </h4>
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm">成交量确认</span>
                          <span
                            className={`text-sm font-medium ${getProbabilityColor(
                              probability.conditionalProbabilities
                                .breakoutSuccess.givenVolume
                            )}`}
                          >
                            {
                              probability.conditionalProbabilities
                                .breakoutSuccess.givenVolume
                            }
                            %
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">动量支持</span>
                          <span
                            className={`text-sm font-medium ${getProbabilityColor(
                              probability.conditionalProbabilities
                                .breakoutSuccess.givenMomentum
                            )}`}
                          >
                            {
                              probability.conditionalProbabilities
                                .breakoutSuccess.givenMomentum
                            }
                            %
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">庄家支持</span>
                          <span
                            className={`text-sm font-medium ${getProbabilityColor(
                              probability.conditionalProbabilities
                                .breakoutSuccess.givenMakerSupport
                            )}`}
                          >
                            {
                              probability.conditionalProbabilities
                                .breakoutSuccess.givenMakerSupport
                            }
                            %
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 期望值分析 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Target className="h-4 w-4" />
                    <span>期望值与最优仓位分析</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-semibold mb-3">交易期望值</h4>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-500">
                            数学期望
                          </span>
                          <span
                            className={`font-medium ${
                              probability.expectedValue.tradeExpectation >= 0
                                ? "text-green-600"
                                : "text-red-600"
                            }`}
                          >
                            {probability.expectedValue.tradeExpectation >= 0
                              ? "+"
                              : ""}
                            {probability.expectedValue.tradeExpectation.toFixed(
                              2
                            )}
                            %
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-500">
                            风险调整收益
                          </span>
                          <span
                            className={`font-medium ${
                              probability.expectedValue.riskAdjustedReturn >= 0
                                ? "text-green-600"
                                : "text-red-600"
                            }`}
                          >
                            {probability.expectedValue.riskAdjustedReturn >= 0
                              ? "+"
                              : ""}
                            {probability.expectedValue.riskAdjustedReturn.toFixed(
                              2
                            )}
                            %
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-500">
                            预期回撤
                          </span>
                          <span className="font-medium text-red-600">
                            -
                            {probability.expectedValue.expectedDrawdown.toFixed(
                              2
                            )}
                            %
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="p-4 border rounded-lg">
                      <h4 className="font-semibold mb-3">最优仓位计算</h4>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-500">
                            凯利公式建议
                          </span>
                          <span className="font-medium text-blue-600">
                            {probability.expectedValue.kellyPercentage.toFixed(
                              1
                            )}
                            %
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-500">
                            最优仓位大小
                          </span>
                          <span className="font-medium text-blue-600">
                            {probability.expectedValue.optimalPositionSize.toFixed(
                              1
                            )}
                            %
                          </span>
                        </div>
                        <div className="mt-3 p-2 bg-blue-50 rounded">
                          <div className="flex items-center space-x-2">
                            <Info className="h-4 w-4 text-blue-500" />
                            <span className="text-sm text-blue-700">
                              基于概率论的最优仓位配置建议
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 风险概率评估 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <AlertTriangle className="h-4 w-4" />
                    <span>风险概率评估</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div className="flex justify-between items-center p-3 border rounded">
                        <span className="text-sm">止损触发概率</span>
                        <div className="flex items-center space-x-2">
                          <Progress
                            value={probability.riskProbabilities.stopLossHit}
                            className="w-20"
                          />
                          <span
                            className={`text-sm font-medium ${getProbabilityColor(
                              100 - probability.riskProbabilities.stopLossHit
                            )}`}
                          >
                            {probability.riskProbabilities.stopLossHit}%
                          </span>
                        </div>
                      </div>

                      <div className="flex justify-between items-center p-3 border rounded">
                        <span className="text-sm">止盈达成概率</span>
                        <div className="flex items-center space-x-2">
                          <Progress
                            value={probability.riskProbabilities.takeProfitHit}
                            className="w-20"
                          />
                          <span
                            className={`text-sm font-medium ${getProbabilityColor(
                              probability.riskProbabilities.takeProfitHit
                            )}`}
                          >
                            {probability.riskProbabilities.takeProfitHit}%
                          </span>
                        </div>
                      </div>

                      <div className="flex justify-between items-center p-3 border rounded">
                        <span className="text-sm">最大回撤超限概率</span>
                        <div className="flex items-center space-x-2">
                          <Progress
                            value={
                              probability.riskProbabilities.maxDrawdownExceeded
                            }
                            className="w-20"
                          />
                          <span
                            className={`text-sm font-medium ${getProbabilityColor(
                              100 -
                                probability.riskProbabilities
                                  .maxDrawdownExceeded
                            )}`}
                          >
                            {probability.riskProbabilities.maxDrawdownExceeded}%
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex justify-between items-center p-3 border rounded">
                        <span className="text-sm">极端事件概率</span>
                        <div className="flex items-center space-x-2">
                          <Progress
                            value={probability.riskProbabilities.extremeEvent}
                            className="w-20"
                          />
                          <span
                            className={`text-sm font-medium ${getProbabilityColor(
                              100 - probability.riskProbabilities.extremeEvent
                            )}`}
                          >
                            {probability.riskProbabilities.extremeEvent}%
                          </span>
                        </div>
                      </div>

                      <div className="flex justify-between items-center p-3 border rounded">
                        <span className="text-sm">爆仓风险概率</span>
                        <div className="flex items-center space-x-2">
                          <Progress
                            value={
                              probability.riskProbabilities.liquidationRisk
                            }
                            className="w-20"
                          />
                          <span
                            className={`text-sm font-medium ${getProbabilityColor(
                              100 -
                                probability.riskProbabilities.liquidationRisk
                            )}`}
                          >
                            {probability.riskProbabilities.liquidationRisk}%
                          </span>
                        </div>
                      </div>

                      <div className="p-3 bg-yellow-50 border border-yellow-200 rounded">
                        <div className="flex items-center space-x-2">
                          <AlertTriangle className="h-4 w-4 text-yellow-600" />
                          <span className="text-sm text-yellow-800 font-medium">
                            风险提示
                          </span>
                        </div>
                        <p className="text-xs text-yellow-700 mt-1">
                          概率分析仅供参考，实际交易中请结合多种因素综合判断
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
