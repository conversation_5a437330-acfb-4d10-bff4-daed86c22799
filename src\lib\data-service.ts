import axios from "axios";
import {
  KlineData,
  ProcessedKlineData,
  MarketData,
  CoinInfo,
} from "@/types/trading";
import { tradingConfigService } from "./trading-config-service";

// 默认交易所API配置
const DEFAULT_EXCHANGE_CONFIG = {
  baseURL: "https://fapi.asterdex.com",
  endpoints: {
    klines: "/fapi/v1/klines",
    ticker24hr: "/fapi/v1/ticker/24hr",
    exchangeInfo: "/fapi/v1/exchangeInfo",
    markPrice: "/fapi/v1/premiumIndex",
  },
};

// 备用数据源（币安API）
const BINANCE_CONFIG = {
  baseURL: "https://fapi.binance.com",
  endpoints: {
    klines: "/fapi/v1/klines",
    ticker24hr: "/fapi/v1/ticker/24hr",
  },
};

export class DataService {
  private static instance: DataService;
  private cache: Map<string, { data: any; timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 2000; // 2秒缓存，支持实时轮询

  static getInstance(): DataService {
    if (!DataService.instance) {
      DataService.instance = new DataService();
    }
    return DataService.instance;
  }

  /**
   * 获取当前交易所配置
   */
  private getExchangeConfig() {
    const tradingConfig = tradingConfigService.getConfig();

    // 如果有配置的交易API，使用配置的baseURL
    if (tradingConfig.baseURL) {
      return {
        baseURL: tradingConfig.baseURL,
        endpoints: DEFAULT_EXCHANGE_CONFIG.endpoints,
      };
    }

    // 否则使用默认配置
    return DEFAULT_EXCHANGE_CONFIG;
  }

  private async fetchWithFallback(
    url: string,
    params: any,
    useBinance = false
  ): Promise<any> {
    const config = useBinance ? BINANCE_CONFIG : this.getExchangeConfig();

    try {
      const response = await axios.get(`${config.baseURL}${url}`, {
        params,
        timeout: 10000,
      });
      return response.data;
    } catch (error) {
      if (!useBinance) {
        console.warn("主API失败，尝试备用数据源...");
        return this.fetchWithFallback(url, params, true);
      }
      throw error;
    }
  }

  private getCacheKey(symbol: string, interval: string, limit: number): string {
    return `${symbol}_${interval}_${limit}`;
  }

  private getFromCache(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data;
    }
    return null;
  }

  private setCache(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  /**
   * 获取K线数据
   */
  async getKlineData(
    symbol: string,
    interval: string,
    limit: number,
    startTime?: number,
    endTime?: number
  ): Promise<KlineData[]> {
    const cacheKey = this.getCacheKey(symbol, interval, limit);
    const cached = this.getFromCache(cacheKey);

    if (cached) {
      return cached;
    }

    const params: any = {
      symbol: symbol.toUpperCase(),
      interval,
      limit,
    };

    if (startTime) params.startTime = startTime;
    if (endTime) params.endTime = endTime;

    try {
      const data = await this.fetchWithFallback("/fapi/v1/klines", params);

      const klineData: KlineData[] = data.map((item: any[]) => ({
        openTime: item[0],
        open: item[1],
        high: item[2],
        low: item[3],
        close: item[4],
        volume: item[5],
        closeTime: item[6],
        quoteAssetVolume: item[7],
        numberOfTrades: item[8],
        takerBuyBaseAssetVolume: item[9],
        takerBuyQuoteAssetVolume: item[10],
      }));

      this.setCache(cacheKey, klineData);
      return klineData;
    } catch (error) {
      console.error("获取K线数据失败:", error);
      throw new Error(`获取${symbol}的K线数据失败`);
    }
  }

  /**
   * 处理K线数据，计算涨跌幅等
   */
  private processKlineData(klineData: KlineData[]): ProcessedKlineData[] {
    return klineData.map((item, index) => {
      const open = parseFloat(item.open);
      const high = parseFloat(item.high);
      const low = parseFloat(item.low);
      const close = parseFloat(item.close);
      const volume = parseFloat(item.volume);

      const change =
        index > 0 ? close - parseFloat(klineData[index - 1].close) : 0;
      const changePercent =
        index > 0 ? (change / parseFloat(klineData[index - 1].close)) * 100 : 0;

      return {
        timestamp: item.openTime,
        open,
        high,
        low,
        close,
        volume,
        change,
        changePercent,
      };
    });
  }

  /**
   * 获取多时间维度的市场数据
   */
  async getMarketData(symbol: string): Promise<MarketData> {
    try {
      const now = Date.now();

      // 并行获取不同时间维度的数据
      const [monthlyData, dailyData, hourlyData, thirtyMinData, oneMinData] =
        await Promise.all([
          this.getKlineData(symbol, "1M", 36), // 36个月
          this.getKlineData(symbol, "1d", 30), // 30天
          this.getKlineData(symbol, "1h", 168), // 7天 * 24小时
          this.getKlineData(symbol, "30m", 48), // 1天 * 48个30分钟
          this.getKlineData(symbol, "1m", 60), // 1小时 * 60分钟
        ]);

      return {
        symbol: symbol.toUpperCase(),
        monthly: this.processKlineData(monthlyData),
        daily: this.processKlineData(dailyData),
        hourly: this.processKlineData(hourlyData),
        thirtyMin: this.processKlineData(thirtyMinData),
        oneMin: this.processKlineData(oneMinData),
      };
    } catch (error) {
      console.error("获取市场数据失败:", error);
      throw new Error(`获取${symbol}的市场数据失败`);
    }
  }

  /**
   * 获取币种信息
   */
  async getCoinInfo(symbol?: string): Promise<CoinInfo[]> {
    try {
      const params = symbol ? { symbol: symbol.toUpperCase() } : {};
      const data = await this.fetchWithFallback("/fapi/v1/ticker/24hr", params);

      const tickers = Array.isArray(data) ? data : [data];

      return tickers.map((ticker: any) => ({
        symbol: ticker.symbol,
        name: ticker.symbol.replace("USDT", ""),
        price: parseFloat(ticker.lastPrice),
        change24h: parseFloat(ticker.priceChangePercent),
        volume24h: parseFloat(ticker.volume),
        marketCap: parseFloat(ticker.quoteVolume),
      }));
    } catch (error) {
      console.error("获取币种信息失败:", error);
      throw new Error("获取币种信息失败");
    }
  }

  /**
   * 获取热门交易对
   */
  async getPopularCoins(): Promise<CoinInfo[]> {
    try {
      const allCoins = await this.getCoinInfo();

      // 过滤USDT交易对并按成交量排序
      return allCoins
        .filter((coin) => coin.symbol.endsWith("USDT"))
        .sort((a, b) => b.volume24h - a.volume24h)
        .slice(0, 20); // 取前20个
    } catch (error) {
      console.error("获取热门币种失败:", error);
      return [];
    }
  }

  /**
   * 分页获取币种信息
   */
  async getPaginatedCoins(params: {
    page: number;
    pageSize: number;
    search?: string;
    sortBy?: "symbol" | "price" | "change24h" | "volume24h";
    sortOrder?: "asc" | "desc";
  }): Promise<{
    data: CoinInfo[];
    pagination: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }> {
    try {
      // 获取所有币种数据
      let allCoins = await this.getCoinInfo();

      // 过滤USDT交易对
      allCoins = allCoins.filter((coin) => coin.symbol.endsWith("USDT"));

      // 搜索过滤
      if (params.search) {
        const searchTerm = params.search.toLowerCase();
        allCoins = allCoins.filter(
          (coin) =>
            coin.symbol.toLowerCase().includes(searchTerm) ||
            coin.name.toLowerCase().includes(searchTerm)
        );
      }

      // 排序
      const sortBy = params.sortBy || "price";
      const sortOrder = params.sortOrder || "desc";

      allCoins.sort((a, b) => {
        let aValue: number;
        let bValue: number;

        switch (sortBy) {
          case "symbol":
            return sortOrder === "asc"
              ? a.symbol.localeCompare(b.symbol)
              : b.symbol.localeCompare(a.symbol);
          case "price":
            aValue = a.price;
            bValue = b.price;
            break;
          case "change24h":
            aValue = a.change24h;
            bValue = b.change24h;
            break;
          case "volume24h":
          default:
            aValue = a.volume24h;
            bValue = b.volume24h;
            break;
        }

        return sortOrder === "asc" ? aValue - bValue : bValue - aValue;
      });

      // 分页计算
      const total = allCoins.length;
      const totalPages = Math.ceil(total / params.pageSize);
      const startIndex = (params.page - 1) * params.pageSize;
      const endIndex = startIndex + params.pageSize;

      const paginatedData = allCoins.slice(startIndex, endIndex);

      return {
        data: paginatedData,
        pagination: {
          page: params.page,
          pageSize: params.pageSize,
          total,
          totalPages,
          hasNext: params.page < totalPages,
          hasPrev: params.page > 1,
        },
      };
    } catch (error) {
      console.error("获取分页币种数据失败:", error);
      throw new Error("获取分页币种数据失败");
    }
  }

  /**
   * 计算技术指标
   */
  calculateTechnicalIndicators(data: ProcessedKlineData[]) {
    if (data.length < 20) {
      throw new Error("数据不足，无法计算技术指标");
    }

    const closes = data.map((d) => d.close);
    const highs = data.map((d) => d.high);
    const lows = data.map((d) => d.low);

    return {
      rsi: this.calculateRSI(closes, 14),
      macd: this.calculateMACD(closes),
      bollinger: this.calculateBollingerBands(closes, 20, 2),
      ema: {
        ema20: this.calculateEMA(closes, 20),
        ema50: this.calculateEMA(closes, 50),
        ema200: this.calculateEMA(closes, 200),
      },
      support: Math.min(...lows.slice(-20)),
      resistance: Math.max(...highs.slice(-20)),
    };
  }

  private calculateRSI(prices: number[], period: number): number {
    if (prices.length < period + 1) return 50;

    let gains = 0;
    let losses = 0;

    for (let i = 1; i <= period; i++) {
      const change = prices[prices.length - i] - prices[prices.length - i - 1];
      if (change > 0) gains += change;
      else losses -= change;
    }

    const avgGain = gains / period;
    const avgLoss = losses / period;
    const rs = avgGain / avgLoss;

    return 100 - 100 / (1 + rs);
  }

  private calculateEMA(prices: number[], period: number): number {
    if (prices.length < period) return prices[prices.length - 1];

    const multiplier = 2 / (period + 1);
    let ema =
      prices.slice(0, period).reduce((sum, price) => sum + price, 0) / period;

    for (let i = period; i < prices.length; i++) {
      ema = prices[i] * multiplier + ema * (1 - multiplier);
    }

    return ema;
  }

  private calculateMACD(prices: number[]) {
    const ema12 = this.calculateEMA(prices, 12);
    const ema26 = this.calculateEMA(prices, 26);
    const macd = ema12 - ema26;

    // 简化的信号线计算
    const signal = macd * 0.9; // 简化处理
    const histogram = macd - signal;

    return { macd, signal, histogram };
  }

  private calculateBollingerBands(
    prices: number[],
    period: number,
    stdDev: number
  ) {
    const sma =
      prices.slice(-period).reduce((sum, price) => sum + price, 0) / period;
    const variance =
      prices
        .slice(-period)
        .reduce((sum, price) => sum + Math.pow(price - sma, 2), 0) / period;
    const standardDeviation = Math.sqrt(variance);

    return {
      upper: sma + standardDeviation * stdDev,
      middle: sma,
      lower: sma - standardDeviation * stdDev,
    };
  }
}
