"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  Loader2,
  RefreshC<PERSON>,
  AlertCircle,
  TrendingUp,
  Settings,
  Play,
  Pause,
} from "lucide-react";
import Link from "next/link";
import { PaginatedCoinSelector } from "@/components/paginated-coin-selector";
import { AnalysisResult } from "@/components/analysis-result";
import { KlineChart } from "@/components/kline-chart";
import { TradingAdvice } from "@/components/trading-advice";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

import { toast } from "sonner";
import { configService } from "@/lib/config-service";
import { tradingConfigService } from "@/lib/trading-config-service";
import { usePolling } from "@/hooks/use-polling";

// 风险偏好描述配置
const riskToleranceOptions = {
  LOW: {
    label: "保守型",
    description: "低风险策略，注重资金安全",
    details: "仓位大小: 1-3% | 止损严格 | 适合新手投资者",
    characteristics: [
      "单次仓位不超过3%",
      "严格止损控制",
      "优先选择低波动币种",
      "保守的加仓策略",
    ],
  },
  MEDIUM: {
    label: "平衡型",
    description: "平衡风险与收益，适中策略",
    details: "仓位大小: 2-5% | 风险可控 | 适合有经验投资者",
    characteristics: [
      "单次仓位2-5%",
      "灵活的风险控制",
      "平衡的加仓策略",
      "适度的杠杆使用",
    ],
  },
  HIGH: {
    label: "激进型",
    description: "追求高收益，承担较高风险",
    details: "仓位大小: 3-8% | 高风险高收益 | 适合专业投资者",
    characteristics: [
      "单次仓位可达8%",
      "积极的加仓策略",
      "较高的杠杆使用",
      "快速止盈止损",
    ],
  },
};

export default function TradingPage() {
  const [selectedCoin, setSelectedCoin] = useState<string>("BTCUSDT");
  const [analysisData, setAnalysisData] = useState<any>(null);
  const [marketData, setMarketData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [riskTolerance, setRiskTolerance] = useState<"LOW" | "MEDIUM" | "HIGH">(
    "MEDIUM"
  );
  const [lastAnalysisTime, setLastAnalysisTime] = useState<Date | null>(null);
  const [isConfigured, setIsConfigured] = useState(false);
  const [isTradingConfigured, setIsTradingConfigured] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("chart");
  const [lastUpdateTime, setLastUpdateTime] = useState<Date | null>(null);

  useEffect(() => {
    if (selectedCoin) {
      fetchMarketData();
    }
  }, [selectedCoin]);

  useEffect(() => {
    // 检查AI配置状态 - 直接检查配置服务
    const config = configService.getConfig();
    setIsConfigured(!!(config.apiKey && config.apiKey.trim()));

    // 检查交易API配置状态
    const tradingConfigured = tradingConfigService.isConfigured();
    setIsTradingConfigured(tradingConfigured);
  }, []);

  // 添加一个函数来重新检查配置状态
  const recheckConfiguration = () => {
    const config = configService.getConfig();
    setIsConfigured(!!(config.apiKey && config.apiKey.trim()));

    const tradingConfigured = tradingConfigService.isConfigured();
    setIsTradingConfigured(tradingConfigured);
  };

  // 监听页面焦点变化，当用户从设置页面返回时重新检查配置
  useEffect(() => {
    const handleFocus = () => {
      recheckConfiguration();
    };

    window.addEventListener("focus", handleFocus);
    return () => window.removeEventListener("focus", handleFocus);
  }, []);

  const fetchMarketData = useCallback(
    async (silent = false) => {
      let loadingToast: string | number | undefined;

      if (!silent) {
        loadingToast = toast.loading("正在获取市场数据...", {
          description: `正在获取 ${selectedCoin} 的实时数据`,
        });
      }

      try {
        setError(null);
        const response = await fetch(`/api/klines?symbol=${selectedCoin}`);
        const result = await response.json();

        if (result.success) {
          setMarketData(result.data);
          setLastUpdateTime(new Date());
          if (!silent) {
            toast.dismiss(loadingToast);
            toast.success("✅ 市场数据获取成功！", {
              description: `${selectedCoin} 的多时间维度数据已更新`,
              duration: 3000,
            });
          }
        } else {
          const errorMsg = result.error || "获取市场数据失败";
          setError(errorMsg);
          if (!silent) {
            toast.dismiss(loadingToast);
            toast.error("❌ 获取市场数据失败", {
              description: errorMsg,
              duration: 4000,
            });
          }
        }
      } catch (error) {
        console.error("获取市场数据失败:", error);
        const errorMsg = "获取市场数据失败，请检查网络连接";
        setError(errorMsg);
        if (!silent) {
          toast.dismiss(loadingToast);
          toast.error("❌ 网络连接失败", {
            description: errorMsg,
            duration: 4000,
          });
        }
      }
    },
    [selectedCoin]
  );

  // 轮询回调函数
  const pollingCallback = useCallback(async () => {
    if (selectedCoin) {
      await fetchMarketData(true); // 静默更新
    }
  }, [selectedCoin, fetchMarketData]);

  // 轮询控制
  const polling = usePolling(pollingCallback, {
    interval: 5000, // 5秒轮询
    immediate: false, // 不立即执行，等待手动获取数据后再开始轮询
    enabled: !!selectedCoin, // 只有选择了币种才启用轮询
  });

  const performAnalysis = async () => {
    if (!selectedCoin) {
      toast.error("❌ 请先选择币种", {
        description: "请在上方选择要分析的加密货币",
        duration: 3000,
      });
      return;
    }

    // 获取当前配置
    const config = configService.getConfig();
    if (!config.apiKey || !config.apiKey.trim()) {
      toast.error("❌ 配置缺失", {
        description: "请先配置 OpenAI API 设置",
        duration: 4000,
        action: {
          label: "前往设置",
          onClick: () => window.open("/settings", "_blank"),
        },
      });
      return;
    }

    const loadingToast = toast.loading("🤖 AI正在分析中...", {
      description: `正在分析 ${selectedCoin} 的市场数据和技术指标`,
    });

    try {
      setLoading(true);
      setError(null);

      const response = await fetch("/api/analyze", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          symbol: selectedCoin,
          riskTolerance,
          includeIndicators: true,
          openaiConfig: config,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setAnalysisData(result.data);
        setLastAnalysisTime(new Date());

        // AI分析完成后自动切换到分析结果页面
        setActiveTab("analysis");

        toast.dismiss(loadingToast);
        toast.success("🎉 AI分析完成！", {
          description: `${selectedCoin} 的智能分析报告已生成，包含交易建议和风险评估`,
          duration: 4000,
        });
      } else {
        const errorMsg = result.error || "AI分析失败";
        setError(errorMsg);
        toast.dismiss(loadingToast);
        toast.error("❌ AI分析失败", {
          description: errorMsg,
          duration: 5000,
        });
      }
    } catch (error) {
      console.error("AI分析失败:", error);
      const errorMsg = "AI分析失败，请稍后重试";
      setError(errorMsg);
      toast.dismiss(loadingToast);
      toast.error("❌ 分析服务异常", {
        description: errorMsg,
        duration: 5000,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCoinSelect = (symbol: string) => {
    setSelectedCoin(symbol);
    setAnalysisData(null);
    setError(null);
    setLastAnalysisTime(null);

    // 重置到图表页面
    setActiveTab("chart");

    // 自动获取市场数据
    if (symbol) {
      setTimeout(() => {
        fetchMarketData();
      }, 500); // 延迟500ms让toast显示完成
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* 页面标题 */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold flex items-center justify-center space-x-2">
          <TrendingUp className="h-8 w-8 text-blue-500" />
          <span>AI加密货币交易分析</span>
        </h1>
        <p className="text-gray-600">
          基于滚仓策略的智能交易分析，助您把握市场机会
        </p>
      </div>

      {/* 币种选择器 */}
      <PaginatedCoinSelector
        selectedCoin={selectedCoin}
        onCoinSelect={handleCoinSelect}
      />

      {/* 分析控制面板 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>分析设置</span>
            {lastAnalysisTime && (
              <Badge variant="outline">
                最后分析: {formatTime(lastAnalysisTime)}
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* 风险偏好选择器 */}
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <span className="text-sm font-medium text-gray-700">
                  风险偏好:
                </span>
                <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
                  {Object.entries(riskToleranceOptions).map(([key, option]) => (
                    <button
                      key={key}
                      onClick={() => setRiskTolerance(key as any)}
                      className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                        riskTolerance === key
                          ? "bg-white text-gray-900 shadow-sm"
                          : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                      }`}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>
              </div>

              {/* 当前选择的风险偏好详情卡片 */}
              <div className="relative overflow-hidden rounded-xl border border-gray-200 bg-white shadow-sm">
                <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-gray-50"></div>
                <div className="relative p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div
                        className={`w-3 h-3 rounded-full ${
                          riskTolerance === "LOW"
                            ? "bg-green-500"
                            : riskTolerance === "MEDIUM"
                            ? "bg-yellow-500"
                            : "bg-red-500"
                        }`}
                      ></div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          {riskToleranceOptions[riskTolerance].label}策略
                        </h3>
                        <p className="text-sm text-gray-600">
                          {riskToleranceOptions[riskTolerance].description}
                        </p>
                      </div>
                    </div>
                    <div
                      className={`px-3 py-1 rounded-full text-xs font-medium ${
                        riskTolerance === "LOW"
                          ? "bg-green-100 text-green-700"
                          : riskTolerance === "MEDIUM"
                          ? "bg-yellow-100 text-yellow-700"
                          : "bg-red-100 text-red-700"
                      }`}
                    >
                      {riskToleranceOptions[riskTolerance].details}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    {riskToleranceOptions[riskTolerance].characteristics.map(
                      (char, index) => (
                        <div
                          key={index}
                          className="flex items-center space-x-2 p-2 bg-gray-50 rounded-lg"
                        >
                          <div
                            className={`w-2 h-2 rounded-full ${
                              riskTolerance === "LOW"
                                ? "bg-green-400"
                                : riskTolerance === "MEDIUM"
                                ? "bg-yellow-400"
                                : "bg-red-400"
                            }`}
                          ></div>
                          <span className="text-sm text-gray-700">{char}</span>
                        </div>
                      )
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex items-center space-x-4">
              <Button
                onClick={performAnalysis}
                disabled={loading || !selectedCoin || !isConfigured}
                className="flex items-center space-x-2"
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
                <span>{loading ? "分析中..." : "开始AI分析"}</span>
              </Button>

              {marketData && (
                <Button
                  variant="outline"
                  onClick={() => fetchMarketData()}
                  className="flex items-center space-x-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  <span>刷新数据</span>
                </Button>
              )}

              {/* 轮询控制按钮 */}
              {marketData && (
                <Button
                  variant="outline"
                  onClick={polling.toggle}
                  className="flex items-center space-x-2"
                >
                  {polling.isPolling ? (
                    <Pause className="h-4 w-4" />
                  ) : (
                    <Play className="h-4 w-4" />
                  )}
                  <span>
                    {polling.isPolling ? "暂停轮询" : "开始轮询"}
                    {polling.isPolling && polling.countdown > 0 && (
                      <span className="ml-1 text-xs">
                        ({polling.countdown}s)
                      </span>
                    )}
                  </span>
                </Button>
              )}

              <Link href="/settings">
                <Button
                  variant="outline"
                  className="flex items-center space-x-2"
                >
                  <Settings className="h-4 w-4" />
                  <span>设置</span>
                </Button>
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 配置提示 */}
      {(!isConfigured || !isTradingConfigured) && (
        <div className="space-y-3">
          {!isConfigured && (
            <Alert>
              <Settings className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>AI 分析功能需要配置 OpenAI API 设置才能使用</span>
                <Link href="/settings">
                  <Button size="sm" variant="outline">
                    前往设置
                  </Button>
                </Link>
              </AlertDescription>
            </Alert>
          )}

          {!isTradingConfigured && (
            <Alert>
              <Settings className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>实际交易功能需要配置交易所 API 设置才能使用</span>
                <Link href="/settings">
                  <Button size="sm" variant="outline">
                    配置交易API
                  </Button>
                </Link>
              </AlertDescription>
            </Alert>
          )}
        </div>
      )}

      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 主要内容区域 */}
      {marketData && (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="chart">价格图表</TabsTrigger>
            <TabsTrigger value="analysis" disabled={!analysisData}>
              AI分析结果
            </TabsTrigger>
            <TabsTrigger value="advice" disabled={!analysisData}>
              交易建议
            </TabsTrigger>
          </TabsList>

          <TabsContent value="chart" className="mt-6">
            <KlineChart
              data={marketData}
              symbol={selectedCoin}
              lastUpdateTime={lastUpdateTime}
              isPolling={polling.isPolling}
              countdown={polling.countdown}
            />
          </TabsContent>

          <TabsContent value="analysis" className="mt-6">
            {analysisData ? (
              <AnalysisResult data={analysisData} />
            ) : (
              <Card>
                <CardContent className="flex items-center justify-center py-12">
                  <div className="text-center space-y-3">
                    <TrendingUp className="h-12 w-12 mx-auto text-gray-400" />
                    <p className="text-gray-500">请先进行AI分析</p>
                    <Button onClick={performAnalysis} disabled={loading}>
                      {loading ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          分析中...
                        </>
                      ) : (
                        "开始分析"
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="advice" className="mt-6">
            {analysisData ? (
              <TradingAdvice
                advice={analysisData.advice}
                positionManagement={analysisData.positionManagement}
                rollingStrategy={analysisData.rollingStrategy}
                riskReward={analysisData.riskReward}
                symbol={selectedCoin}
                multiTimeframePrediction={
                  analysisData.analysis?.multiTimeframePrediction
                }
                gameTheoryAnalysis={analysisData.analysis?.gameTheoryAnalysis}
                probabilityAnalysis={analysisData.analysis?.probabilityAnalysis}
              />
            ) : (
              <Card>
                <CardContent className="flex items-center justify-center py-12">
                  <div className="text-center space-y-3">
                    <TrendingUp className="h-12 w-12 mx-auto text-gray-400" />
                    <p className="text-gray-500">
                      请先进行AI分析以获取交易建议
                    </p>
                    <Button onClick={performAnalysis} disabled={loading}>
                      {loading ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          分析中...
                        </>
                      ) : (
                        "开始分析"
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      )}

      {/* 加载状态 */}
      {!marketData && !error && selectedCoin && (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center space-y-3">
              <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-500" />
              <p className="text-gray-500">
                正在获取 {selectedCoin} 的市场数据...
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 免责声明 */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>免责声明:</strong>
          本工具提供的分析和建议仅供参考，不构成投资建议。
          加密货币交易存在高风险，请根据自身情况谨慎决策，并做好风险管理。
        </AlertDescription>
      </Alert>
    </div>
  );
}
