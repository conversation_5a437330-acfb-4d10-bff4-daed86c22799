/**
 * 游戏论分析模块
 * 基于博弈论原理分析市场参与者行为和策略选择
 */

import { ProcessedKlineData } from "@/types/trading";

export interface GameTheoryAnalysis {
  // 市场参与者分析
  marketParticipants: {
    bulls: { strength: number; confidence: number; strategy: string };
    bears: { strength: number; confidence: number; strategy: string };
    whales: { activity: number; direction: "BUY" | "SELL" | "NEUTRAL" };
    retail: { sentiment: number; flow: "IN" | "OUT" | "NEUTRAL" };
  };
  
  // 纳什均衡分析
  nashEquilibrium: {
    currentState: "STABLE" | "UNSTABLE" | "TRANSITIONING";
    equilibriumPrice: number;
    deviationRisk: number; // 偏离均衡的风险
    timeToEquilibrium: number; // 达到均衡的预期时间(小时)
  };
  
  // 策略矩阵
  strategyMatrix: {
    timeframe: "SHORT" | "MEDIUM" | "LONG";
    playerStrategies: {
      aggressive: { expectedReturn: number; risk: number; probability: number };
      conservative: { expectedReturn: number; risk: number; probability: number };
      contrarian: { expectedReturn: number; risk: number; probability: number };
    };
    optimalStrategy: "aggressive" | "conservative" | "contrarian";
  }[];
  
  // 信息不对称分析
  informationAsymmetry: {
    level: "HIGH" | "MEDIUM" | "LOW";
    advantagedPlayers: string[];
    informationValue: number; // 信息的价值评估
    leakageRate: number; // 信息泄露速度
  };
  
  // 市场操纵风险
  manipulationRisk: {
    level: "HIGH" | "MEDIUM" | "LOW";
    indicators: string[];
    probability: number;
    protectionStrategy: string;
  };
}

export class GameTheoryAnalyzer {
  /**
   * 分析市场参与者行为
   */
  analyzeMarketParticipants(
    data: ProcessedKlineData[],
    volumeProfile: any
  ): GameTheoryAnalysis["marketParticipants"] {
    const recentData = data.slice(-24); // 最近24小时
    
    // 分析多空力量
    const bullBearAnalysis = this.analyzeBullBearStrength(recentData);
    
    // 分析大户行为
    const whaleAnalysis = this.analyzeWhaleActivity(recentData, volumeProfile);
    
    // 分析散户情绪
    const retailAnalysis = this.analyzeRetailSentiment(recentData);
    
    return {
      bulls: {
        strength: bullBearAnalysis.bullStrength,
        confidence: bullBearAnalysis.bullConfidence,
        strategy: this.determineBullStrategy(bullBearAnalysis)
      },
      bears: {
        strength: bullBearAnalysis.bearStrength,
        confidence: bullBearAnalysis.bearConfidence,
        strategy: this.determineBearStrategy(bullBearAnalysis)
      },
      whales: {
        activity: whaleAnalysis.activity,
        direction: whaleAnalysis.direction,
      },
      retail: {
        sentiment: retailAnalysis.sentiment,
        flow: retailAnalysis.flow
      }
    };
  }

  /**
   * 计算纳什均衡
   */
  calculateNashEquilibrium(
    data: ProcessedKlineData[],
    marketParticipants: GameTheoryAnalysis["marketParticipants"]
  ): GameTheoryAnalysis["nashEquilibrium"] {
    const currentPrice = data[data.length - 1].close;
    
    // 基于参与者力量计算均衡价格
    const equilibriumPrice = this.calculateEquilibriumPrice(
      currentPrice,
      marketParticipants
    );
    
    // 评估当前状态稳定性
    const stability = this.assessMarketStability(data, marketParticipants);
    
    // 计算偏离风险
    const deviationRisk = this.calculateDeviationRisk(
      currentPrice,
      equilibriumPrice,
      stability
    );
    
    return {
      currentState: stability.state,
      equilibriumPrice,
      deviationRisk,
      timeToEquilibrium: stability.timeToEquilibrium
    };
  }

  /**
   * 生成策略矩阵
   */
  generateStrategyMatrix(
    data: ProcessedKlineData[],
    marketConditions: any
  ): GameTheoryAnalysis["strategyMatrix"] {
    const timeframes: Array<"SHORT" | "MEDIUM" | "LONG"> = ["SHORT", "MEDIUM", "LONG"];
    
    return timeframes.map(timeframe => {
      const strategies = this.calculateStrategyPayoffs(data, timeframe, marketConditions);
      const optimalStrategy = this.findOptimalStrategy(strategies);
      
      return {
        timeframe,
        playerStrategies: strategies,
        optimalStrategy
      };
    });
  }

  /**
   * 分析信息不对称
   */
  analyzeInformationAsymmetry(
    data: ProcessedKlineData[],
    marketEvents: any[]
  ): GameTheoryAnalysis["informationAsymmetry"] {
    // 分析价格发现效率
    const priceDiscoveryEfficiency = this.calculatePriceDiscoveryEfficiency(data);
    
    // 识别信息优势方
    const advantagedPlayers = this.identifyInformationAdvantage(data, marketEvents);
    
    // 评估信息价值
    const informationValue = this.calculateInformationValue(data);
    
    // 计算信息泄露速度
    const leakageRate = this.calculateInformationLeakage(data);
    
    const asymmetryLevel = this.determineAsymmetryLevel(
      priceDiscoveryEfficiency,
      informationValue
    );
    
    return {
      level: asymmetryLevel,
      advantagedPlayers,
      informationValue,
      leakageRate
    };
  }

  /**
   * 评估市场操纵风险
   */
  assessManipulationRisk(
    data: ProcessedKlineData[],
    volumeProfile: any
  ): GameTheoryAnalysis["manipulationRisk"] {
    const indicators = [];
    let riskScore = 0;
    
    // 检查异常价格波动
    const priceAnomalies = this.detectPriceAnomalies(data);
    if (priceAnomalies.length > 0) {
      indicators.push("异常价格波动");
      riskScore += 30;
    }
    
    // 检查成交量异常
    const volumeAnomalies = this.detectVolumeAnomalies(data, volumeProfile);
    if (volumeAnomalies.length > 0) {
      indicators.push("成交量异常");
      riskScore += 25;
    }
    
    // 检查订单簿异常
    const orderBookAnomalies = this.detectOrderBookAnomalies(data);
    if (orderBookAnomalies.length > 0) {
      indicators.push("订单簿异常");
      riskScore += 20;
    }
    
    // 检查时间模式
    const timePatterns = this.detectSuspiciousTimePatterns(data);
    if (timePatterns.length > 0) {
      indicators.push("可疑时间模式");
      riskScore += 15;
    }
    
    const riskLevel = riskScore > 60 ? "HIGH" : riskScore > 30 ? "MEDIUM" : "LOW";
    const protectionStrategy = this.generateProtectionStrategy(riskLevel, indicators);
    
    return {
      level: riskLevel,
      indicators,
      probability: Math.min(95, riskScore),
      protectionStrategy
    };
  }

  // 私有辅助方法
  private analyzeBullBearStrength(data: ProcessedKlineData[]) {
    const priceChanges = data.map((d, i) => 
      i > 0 ? (d.close - data[i-1].close) / data[i-1].close : 0
    ).slice(1);
    
    const positiveChanges = priceChanges.filter(c => c > 0);
    const negativeChanges = priceChanges.filter(c => c < 0);
    
    const bullStrength = (positiveChanges.length / priceChanges.length) * 100;
    const bearStrength = (negativeChanges.length / priceChanges.length) * 100;
    
    const avgPositiveChange = positiveChanges.reduce((sum, c) => sum + c, 0) / positiveChanges.length || 0;
    const avgNegativeChange = Math.abs(negativeChanges.reduce((sum, c) => sum + c, 0) / negativeChanges.length || 0);
    
    return {
      bullStrength,
      bearStrength,
      bullConfidence: Math.min(100, avgPositiveChange * 1000),
      bearConfidence: Math.min(100, avgNegativeChange * 1000)
    };
  }

  private analyzeWhaleActivity(data: ProcessedKlineData[], volumeProfile: any) {
    const avgVolume = data.reduce((sum, d) => sum + d.volume, 0) / data.length;
    const recentVolume = data.slice(-6).reduce((sum, d) => sum + d.volume, 0) / 6;
    
    const activity = Math.min(100, (recentVolume / avgVolume) * 50);
    
    // 基于价格和成交量关系判断方向
    const priceVolumeCorrelation = this.calculatePriceVolumeCorrelation(data.slice(-12));
    const direction = priceVolumeCorrelation > 0.3 ? "BUY" : 
                     priceVolumeCorrelation < -0.3 ? "SELL" : "NEUTRAL";
    
    return { activity, direction };
  }

  private analyzeRetailSentiment(data: ProcessedKlineData[]) {
    // 基于小额交易模式分析散户情绪
    const volatility = this.calculateVolatility(data);
    const trend = this.calculateTrend(data);
    
    const sentiment = 50 + trend * 30 - volatility * 20;
    const flow = sentiment > 60 ? "IN" : sentiment < 40 ? "OUT" : "NEUTRAL";
    
    return { sentiment: Math.max(0, Math.min(100, sentiment)), flow };
  }

  private calculateEquilibriumPrice(currentPrice: number, participants: any): number {
    const bullPressure = participants.bulls.strength * participants.bulls.confidence / 10000;
    const bearPressure = participants.bears.strength * participants.bears.confidence / 10000;
    
    const netPressure = bullPressure - bearPressure;
    return currentPrice * (1 + netPressure * 0.1);
  }

  private assessMarketStability(data: ProcessedKlineData[], participants: any) {
    const volatility = this.calculateVolatility(data);
    const participantBalance = Math.abs(participants.bulls.strength - participants.bears.strength);
    
    let state: "STABLE" | "UNSTABLE" | "TRANSITIONING";
    let timeToEquilibrium: number;
    
    if (volatility < 0.02 && participantBalance < 20) {
      state = "STABLE";
      timeToEquilibrium = 2;
    } else if (volatility > 0.05 || participantBalance > 40) {
      state = "UNSTABLE";
      timeToEquilibrium = 12;
    } else {
      state = "TRANSITIONING";
      timeToEquilibrium = 6;
    }
    
    return { state, timeToEquilibrium };
  }

  private calculateDeviationRisk(currentPrice: number, equilibriumPrice: number, stability: any): number {
    const priceDeviation = Math.abs(currentPrice - equilibriumPrice) / equilibriumPrice;
    const stabilityFactor = stability.state === "STABLE" ? 0.5 : 
                           stability.state === "UNSTABLE" ? 2.0 : 1.0;
    
    return Math.min(100, priceDeviation * 100 * stabilityFactor);
  }

  private calculateStrategyPayoffs(data: ProcessedKlineData[], timeframe: string, conditions: any) {
    const volatility = this.calculateVolatility(data);
    const trend = this.calculateTrend(data);
    
    // 基于市场条件计算不同策略的期望收益和风险
    const aggressive = {
      expectedReturn: trend * 0.8 + volatility * 0.3,
      risk: volatility * 1.5,
      probability: 0.3
    };
    
    const conservative = {
      expectedReturn: trend * 0.4,
      risk: volatility * 0.8,
      probability: 0.5
    };
    
    const contrarian = {
      expectedReturn: -trend * 0.6 + volatility * 0.2,
      risk: volatility * 1.2,
      probability: 0.2
    };
    
    return { aggressive, conservative, contrarian };
  }

  private findOptimalStrategy(strategies: any): "aggressive" | "conservative" | "contrarian" {
    const sharpeRatios = {
      aggressive: strategies.aggressive.expectedReturn / strategies.aggressive.risk,
      conservative: strategies.conservative.expectedReturn / strategies.conservative.risk,
      contrarian: strategies.contrarian.expectedReturn / strategies.contrarian.risk
    };
    
    return Object.entries(sharpeRatios).reduce((a, b) => 
      sharpeRatios[a[0] as keyof typeof sharpeRatios] > sharpeRatios[b[0] as keyof typeof sharpeRatios] ? a : b
    )[0] as "aggressive" | "conservative" | "contrarian";
  }

  // 更多私有方法的简化实现
  private calculateVolatility(data: ProcessedKlineData[]): number {
    const returns = data.slice(1).map((d, i) => Math.log(d.close / data[i].close));
    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / returns.length;
    return Math.sqrt(variance);
  }

  private calculateTrend(data: ProcessedKlineData[]): number {
    const firstPrice = data[0].close;
    const lastPrice = data[data.length - 1].close;
    return (lastPrice - firstPrice) / firstPrice;
  }

  private calculatePriceVolumeCorrelation(data: ProcessedKlineData[]): number {
    // 简化的价格成交量相关性计算
    const priceChanges = data.slice(1).map((d, i) => d.close - data[i].close);
    const volumeChanges = data.slice(1).map((d, i) => d.volume - data[i].volume);
    
    // 计算皮尔逊相关系数
    const n = priceChanges.length;
    const sumX = priceChanges.reduce((sum, x) => sum + x, 0);
    const sumY = volumeChanges.reduce((sum, y) => sum + y, 0);
    const sumXY = priceChanges.reduce((sum, x, i) => sum + x * volumeChanges[i], 0);
    const sumX2 = priceChanges.reduce((sum, x) => sum + x * x, 0);
    const sumY2 = volumeChanges.reduce((sum, y) => sum + y * y, 0);
    
    const numerator = n * sumXY - sumX * sumY;
    const denominator = Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));
    
    return denominator === 0 ? 0 : numerator / denominator;
  }

  // 其他私有方法的占位符实现
  private determineBullStrategy(analysis: any): string { return "积极买入"; }
  private determineBearStrategy(analysis: any): string { return "谨慎卖出"; }
  private calculatePriceDiscoveryEfficiency(data: ProcessedKlineData[]): number { return 0.8; }
  private identifyInformationAdvantage(data: ProcessedKlineData[], events: any[]): string[] { return ["机构投资者"]; }
  private calculateInformationValue(data: ProcessedKlineData[]): number { return 0.7; }
  private calculateInformationLeakage(data: ProcessedKlineData[]): number { return 0.3; }
  private determineAsymmetryLevel(efficiency: number, value: number): "HIGH" | "MEDIUM" | "LOW" { return "MEDIUM"; }
  private detectPriceAnomalies(data: ProcessedKlineData[]): any[] { return []; }
  private detectVolumeAnomalies(data: ProcessedKlineData[], profile: any): any[] { return []; }
  private detectOrderBookAnomalies(data: ProcessedKlineData[]): any[] { return []; }
  private detectSuspiciousTimePatterns(data: ProcessedKlineData[]): any[] { return []; }
  private generateProtectionStrategy(level: string, indicators: string[]): string { return "分散投资，设置止损"; }
}
