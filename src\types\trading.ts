// 交易相关的类型定义

export interface KlineData {
  openTime: number;
  open: string;
  high: string;
  low: string;
  close: string;
  volume: string;
  closeTime: number;
  quoteAssetVolume: string;
  numberOfTrades: number;
  takerBuyBaseAssetVolume: string;
  takerBuyQuoteAssetVolume: string;
}

export interface ProcessedKlineData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  change: number;
  changePercent: number;
}

export interface MarketData {
  symbol: string;
  monthly: ProcessedKlineData[]; // 36个月数据
  daily: ProcessedKlineData[]; // 30天数据
  hourly: ProcessedKlineData[]; // 168小时数据（7天）
  thirtyMin: ProcessedKlineData[]; // 48个30分钟数据（1天）
  oneMin: ProcessedKlineData[]; // 60个1分钟数据（1小时）
}

export interface TechnicalIndicators {
  rsi: number;
  macd: {
    macd: number;
    signal: number;
    histogram: number;
  };
  bollinger: {
    upper: number;
    middle: number;
    lower: number;
  };
  ema: {
    ema20: number;
    ema50: number;
    ema200: number;
  };
  support: number;
  resistance: number;
}

export interface TradingSignal {
  direction: "LONG" | "SHORT" | "HOLD";
  confidence: number; // 0-100
  entryPrice: number;
  stopLoss: number;
  takeProfit: number[];
  positionSize: number; // 建议仓位大小（百分比）
  leverage: number; // 建议杠杆倍数
  reasoning: string;
}

export interface RiskManagement {
  maxDrawdown: number;
  riskReward: number;
  winRate: number;
  expectedReturn: number;
}

export interface AIAnalysisResult {
  symbol: string;
  timestamp: number;
  marketTrend: {
    shortTerm: "BULLISH" | "BEARISH" | "NEUTRAL";
    mediumTerm: "BULLISH" | "BEARISH" | "NEUTRAL";
    longTerm: "BULLISH" | "BEARISH" | "NEUTRAL";
  };
  technicalIndicators: TechnicalIndicators;
  tradingSignal: TradingSignal;
  riskManagement: RiskManagement;
  marketCondition: {
    volatility: "HIGH" | "MEDIUM" | "LOW";
    volume: "HIGH" | "MEDIUM" | "LOW";
    momentum: "STRONG" | "WEAK" | "NEUTRAL";
  };
  keyLevels: {
    support: number[];
    resistance: number[];
  };
  // 新增：庄家行为分析
  marketMakerAnalysis: MarketMakerAnalysis;
  // 新增：现货期货分析
  spotFuturesAnalysis: SpotFuturesAnalysis;
  // 新增：多时间段预测分析
  multiTimeframePrediction: MultiTimeframePrediction;
  // 新增：博弈论分析
  gameTheoryAnalysis: GameTheoryAnalysis;
  // 新增：概率论分析
  probabilityAnalysis: ProbabilityAnalysis;
  aiInsights: string;
  warnings: string[];
}

export interface TradingAdvice {
  action: "BUY" | "SELL" | "HOLD";
  entryPrice: number;
  quantity: number;
  stopLoss: number;
  takeProfit: number[];
  timeframe: string;
  confidence: number;
  reasoning: string;
  riskLevel: "LOW" | "MEDIUM" | "HIGH";
}

export interface PositionManagement {
  initialPosition: number;
  addPositions: {
    price: number;
    size: number;
    condition: string;
  }[];
  exitStrategy: {
    partialExits: {
      price: number;
      percentage: number;
    }[];
    stopLoss: number;
    trailingStop: boolean;
  };
}

export interface MarketAnalysisRequest {
  symbol: string;
  timeframes: ("1M" | "1d" | "1h" | "30m" | "1m")[];
  includeIndicators: boolean;
  riskTolerance: "LOW" | "MEDIUM" | "HIGH";
  openaiConfig: OpenAIConfig; // 添加OpenAI配置
}

// OpenAI API 配置相关类型
export interface OpenAIConfig {
  apiKey: string;
  baseURL: string;
  model: string;
}

export interface ConfigFormData {
  apiKey: string;
  baseURL: string;
  model: string;
}

export interface ConfigTestResult {
  success: boolean;
  message: string;
  latency?: number;
}

// 交易API配置相关类型
export interface TradingAPIConfig {
  baseURL: string;
  apiKey: string;
  secretKey: string;
  testnet?: boolean;
  name?: string; // 配置名称，如"主网"、"测试网"等
}

export interface TradingConfigFormData {
  baseURL: string;
  apiKey: string;
  secretKey: string;
  testnet: boolean;
  name: string;
}

export interface TradingConfigTestResult {
  success: boolean;
  message: string;
  latency?: number;
  accountInfo?: any; // 测试成功时返回的账户信息
}

export interface CoinInfo {
  symbol: string;
  name: string;
  price: number;
  change24h: number;
  volume24h: number;
  marketCap: number;
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 分页相关类型
export interface PaginationParams {
  page: number;
  pageSize: number;
  search?: string;
  sortBy?: "symbol" | "price" | "change24h" | "volume24h";
  sortOrder?: "asc" | "desc";
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  error?: string;
}

// 滚仓策略相关类型
export interface RollingStrategy {
  basePosition: number; // 基础仓位百分比
  additionThreshold: number; // 加仓阈值
  maxPosition: number; // 最大仓位
  profitTarget: number; // 盈利目标
  stopLoss: number; // 止损点
  trendConfirmation: boolean; // 趋势确认
}

export interface TrendAnalysis {
  direction: "UP" | "DOWN" | "SIDEWAYS";
  strength: number; // 1-10
  duration: number; // 持续时间（小时）
  breakoutPotential: number; // 突破潜力 0-100
  volumeConfirmation: boolean;
}

// 庄家行为分析相关类型
export interface MarketMakerAnalysis {
  manipulationSignals: {
    washTrading: number; // 对敲交易可能性 0-100
    priceSupport: number; // 价格托盘强度 0-100
    volumeSpike: number; // 异常放量程度 0-100
    falseBreakout: number; // 假突破可能性 0-100
  };
  psychologyAnalysis: {
    fearGreedIndex: number; // 恐惧贪婪指数 0-100
    marketSentiment:
      | "EXTREME_FEAR"
      | "FEAR"
      | "NEUTRAL"
      | "GREED"
      | "EXTREME_GREED";
    retailBehavior:
      | "PANIC_SELLING"
      | "FOMO_BUYING"
      | "RATIONAL"
      | "ACCUMULATING";
    smartMoneyFlow: "INFLOW" | "OUTFLOW" | "NEUTRAL";
  };
  manipulationPatterns: {
    pattern: "PUMP_DUMP" | "ACCUMULATION" | "DISTRIBUTION" | "SQUEEZE" | "NONE";
    confidence: number; // 0-100
    stage: "EARLY" | "MIDDLE" | "LATE" | "COMPLETION";
    timeframe: string; // 预计持续时间
    description: string;
  };
  intentions: {
    primaryGoal:
      | "ACCUMULATE"
      | "DISTRIBUTE"
      | "SQUEEZE_SHORTS"
      | "SQUEEZE_LONGS"
      | "MAINTAIN_RANGE";
    targetPrice: number; // 庄家目标价位
    confidence: number; // 判断信心度 0-100
    reasoning: string;
  };
}

// 现货与期货差异分析
export interface SpotFuturesAnalysis {
  marketType: "SPOT" | "FUTURES";
  basisSpread?: number; // 基差（仅期货）
  fundingRate?: number; // 资金费率（仅期货）
  openInterest?: number; // 持仓量（仅期货）
  leverageImpact?: {
    averageLeverage: number;
    liquidationRisk: "LOW" | "MEDIUM" | "HIGH";
    cascadeLiquidationPotential: number; // 0-100
  };
  arbitrageOpportunities?: {
    spotFuturesPremium: number;
    crossExchangeSpread: number;
    fundingArbitrage: number;
  };
  recommendations: {
    preferredMarket: "SPOT" | "FUTURES" | "BOTH";
    reasoning: string;
    riskAdjustments: string[];
  };
}

// 时间段预测类型定义
export interface TimeframePrediction {
  timeframe: "SHORT" | "MEDIUM" | "LONG"; // 短期(1-4h) | 中期(4-8h) | 长期(8-24h)
  duration: string; // 时间描述，如 "1-4小时"
  trend: "BULLISH" | "BEARISH" | "NEUTRAL";
  confidence: number; // 0-100
  targetPrice: {
    high: number; // 预期最高价
    low: number; // 预期最低价
    most_likely: number; // 最可能价格
  };
  keyEvents: string[]; // 关键事件预测
  riskFactors: string[]; // 风险因素
  opportunities: string[]; // 机会点
  tradingStrategy: {
    action: "BUY" | "SELL" | "HOLD";
    entryPrice: number;
    stopLoss: number;
    takeProfit: number[];
    positionSize: number; // 建议仓位大小（百分比）
    reasoning: string;
  };
  marketConditions: {
    volatility: "HIGH" | "MEDIUM" | "LOW";
    volume: "HIGH" | "MEDIUM" | "LOW";
    momentum: "STRONG" | "WEAK" | "NEUTRAL";
  };
}

// 多时间段预测分析
export interface MultiTimeframePrediction {
  symbol: string;
  timestamp: number;
  currentPrice: number;
  predictions: {
    short: TimeframePrediction; // 1-4小时
    medium: TimeframePrediction; // 4-8小时
    long: TimeframePrediction; // 8-24小时
  };
  overallAssessment: {
    dominantTrend: "BULLISH" | "BEARISH" | "NEUTRAL";
    conflictingSignals: boolean;
    riskLevel: "LOW" | "MEDIUM" | "HIGH";
    recommendation: string;
  };
  correlationAnalysis: {
    timeframeAlignment: number; // 时间段一致性 0-100
    trendStrength: number; // 趋势强度 0-100
    reversal_probability: number; // 反转概率 0-100
  };
}

// 博弈论分析相关类型
export interface GameTheoryAnalysis {
  playerAnalysis: {
    marketMakers: {
      strategy: "ACCUMULATE" | "DISTRIBUTE" | "MANIPULATE" | "MAINTAIN";
      confidence: number; // 0-100
      expectedMove: "UP" | "DOWN" | "SIDEWAYS";
      timeHorizon: "SHORT" | "MEDIUM" | "LONG";
    };
    institutions: {
      strategy: "FOLLOW" | "CONTRARIAN" | "NEUTRAL";
      confidence: number; // 0-100
      positioning: "LONG" | "SHORT" | "NEUTRAL";
      influence: number; // 0-100
    };
    retailers: {
      behavior: "PANIC" | "FOMO" | "RATIONAL" | "ACCUMULATING";
      sentiment: number; // 0-100
      leverage: "HIGH" | "MEDIUM" | "LOW";
      liquidationRisk: number; // 0-100
    };
  };
  equilibriumAnalysis: {
    nashEquilibrium: {
      price: number;
      volume: number;
      stability: number; // 0-100
      description: string;
    };
    dominantStrategy: {
      player: "MAKERS" | "INSTITUTIONS" | "RETAILERS";
      strategy: string;
      reasoning: string;
    };
    informationAsymmetry: {
      level: number; // 0-100
      advantage: "MAKERS" | "INSTITUTIONS" | "RETAILERS";
      impact: "HIGH" | "MEDIUM" | "LOW";
    };
  };
  strategicRecommendation: {
    optimalStrategy: "BUY" | "SELL" | "HOLD" | "WAIT";
    reasoning: string;
    counterStrategy: string;
    riskMitigation: string;
  };
}

// 概率论分析相关类型
export interface ProbabilityAnalysis {
  bayesianInference: {
    priorProbability: {
      bullish: number; // 0-100
      bearish: number; // 0-100
      neutral: number; // 0-100
    };
    likelihood: {
      technicalSignals: number; // 0-100
      volumeConfirmation: number; // 0-100
      marketStructure: number; // 0-100
    };
    posteriorProbability: {
      bullish: number; // 0-100
      bearish: number; // 0-100
      neutral: number; // 0-100
      confidence: number; // 0-100
    };
  };
  conditionalProbabilities: {
    upMove: {
      givenTechnicals: number; // 0-100
      givenVolume: number; // 0-100
      givenMakerActivity: number; // 0-100
    };
    downMove: {
      givenTechnicals: number; // 0-100
      givenVolume: number; // 0-100
      givenMakerActivity: number; // 0-100
    };
    breakoutSuccess: {
      givenVolume: number; // 0-100
      givenMomentum: number; // 0-100
      givenMakerSupport: number; // 0-100
    };
  };
  expectedValue: {
    tradeExpectation: number;
    riskAdjustedReturn: number;
    kellyPercentage: number; // 0-100
    optimalPositionSize: number;
    expectedDrawdown: number;
  };
  riskProbabilities: {
    stopLossHit: number; // 0-100
    takeProfitHit: number; // 0-100
    maxDrawdownExceeded: number; // 0-100
    extremeEvent: number; // 0-100
    liquidationRisk: number; // 0-100
  };
}
